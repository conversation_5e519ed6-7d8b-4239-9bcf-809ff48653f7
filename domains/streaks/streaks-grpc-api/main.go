package main

// Code initially generated by "go run ./domains/tooling/genservice -domain=streaks -feature=streaks -type=grpc-api"; EDIT AT WILL.

import (
	"context"

	"github.com/BeReal-App/backend-go/domains/entity/moment"
	post_client "github.com/BeReal-App/backend-go/domains/entity/post-client"
	"github.com/BeReal-App/backend-go/domains/streaks/streaks-grpc-api/internal"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	pbStreaks "github.com/BeReal-App/backend-go/proto/streaks/v1"
	"github.com/BeReal-App/backend-go/shared/config"
	"github.com/BeReal-App/backend-go/shared/grpc"
	"github.com/BeReal-App/backend-go/shared/spanner"
	"github.com/rs/zerolog"

	gGrpc "google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/keepalive"
)

func main() {
	cfg := Config{}
	ctx, l := config.Bootstrap(context.Background(), "streaks-streaks-grpc-api", &cfg)

	// spanner client
	spannerClient, err := spanner.NewSpannerClient(ctx, l, cfg.Spanner, *cfg.Application.Env)
	if err != nil {
		l.Fatal().Err(err).Msg("cannot use spanner")
	}
	defer spannerClient.Close()

	// moment client
	momentsClient, err := moment.ProvideClient(ctx, cfg.MomentClient, *l, config.CallerID(*cfg.Application.Name))
	if err != nil {
		l.Fatal().Err(err).Msg("error while instantiating moment client")
	}
	defer momentsClient.Close()

	// post client
	postClient, closePostClient, err := post_client.New(*cfg.PostClient.Addr, *cfg.Application.Name)
	if err != nil {
		l.Fatal().Err(err).Msg("error while instantiating post client")
	}
	defer func() {
		if err := closePostClient(); err != nil {
			l.Error().Err(err).Msg("error while closing post client")
		}
	}()

	// user client
	userClient, err := cfg.UserClient.Addr.ClientConn(*cfg.Application.Name, string(config.CallerID(*cfg.Application.Name)))
	if err != nil {
		l.Fatal().Err(err).Msg("error while instantiating user client")
	}
	defer func() {
		if err := userClient.Close(); err != nil {
			l.Error().Err(err).Msg("error while closing user client")
		}
	}()
	userServiceClient := pbUser.NewUserServiceClient(userClient)

	// construct server
	server, err := internal.NewServer(
		spannerClient,
		momentsClient,
		postClient,
		userServiceClient,
	)
	if err != nil {
		l.Fatal().Err(err).Msg("error while instantiating server")
	}

	// API
	grpc.BootstrapGRPCApi(l, &grpc.BootstrapGRPCApiOpts{
		Application: cfg.Application,
		BuildInfo:   cfg.BuildInfo,
		Profiler:    cfg.Profiler,
		Datadog:     cfg.Datadog,
		GrpcAPI:     cfg.GrpcAPI,
		LogInterceptorOpts: &grpc.LogInterceptorOpts{
			ErrCodesLevel: map[codes.Code]zerolog.Level{
				codes.NotFound: zerolog.Disabled,
			},
		},
		GetGrpcServerOpts: func() []gGrpc.ServerOption {
			return []gGrpc.ServerOption{
				// https://medium.com/jamf-engineering/how-three-lines-of-configuration-solved-our-grpc-scaling-issues-in-kubernetes-ca1ff13f7f06
				gGrpc.KeepaliveParams(keepalive.ServerParameters{
					MaxConnectionAge:      *cfg.GrpcAPI.MaxConnectionAge,
					MaxConnectionAgeGrace: *cfg.GrpcAPI.MaxConnectionAgeGrace,
				}),
			}
		},
		RegisterServers: func(srv *gGrpc.Server) {
			pbStreaks.RegisterStreakServiceServer(srv, server)
		},
	})
}
