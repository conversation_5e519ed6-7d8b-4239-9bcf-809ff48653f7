package internal

import (
	"errors"
	"sort"
	"testing"
	"time"

	gS "cloud.google.com/go/spanner"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"

	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	pbCommon "github.com/BeReal-App/backend-go/proto/common/core/v1"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
)

// Test helper functions that don't require mocking

func TestSortGapsByDate(t *testing.T) {
	// Create unsorted gaps
	gaps := []*pbUser.StreakGap{
		{
			Date: &pbCommon.Date{
				Year:  2023,
				Month: 6,
				Day:   15,
			},
			MomentId: "moment-3",
		},
		{
			Date: &pbCommon.Date{
				Year:  2023,
				Month: 1,
				Day:   15,
			},
			MomentId: "moment-1",
		},
		{
			Date: &pbCommon.Date{
				Year:  2023,
				Month: 3,
				Day:   15,
			},
			MomentId: "moment-2",
		},
	}

	// Sort gaps
	sortGapsByDate(gaps)

	// Verify sort order (oldest first)
	require.Equal(t, int32(1), gaps[0].Date.Month) // January (month 1) should be first
	require.Equal(t, int32(3), gaps[1].Date.Month) // March (month 3) should be second
	require.Equal(t, int32(6), gaps[2].Date.Month) // June (month 6) should be last
}

func TestCalculateEstimatedStreakLogic(t *testing.T) {
	// This tests the core logic without needing to mock the server

	// Create test moments (sorted by FiredAt desc)
	now := time.Now()
	sortedMoments := []models.Moment{
		{
			ID:      "moment-1",
			FiredAt: &now,
		},
		{
			ID:      "moment-2",
			FiredAt: func() *time.Time { t := now.AddDate(0, 0, -1); return &t }(),
		},
		{
			ID:      "moment-3",
			FiredAt: func() *time.Time { t := now.AddDate(0, 0, -2); return &t }(),
		},
		{
			ID:      "moment-4",
			FiredAt: func() *time.Time { t := now.AddDate(0, 0, -3); return &t }(),
		},
	}

	// Test scenario 1: User posted for moment-1 and moment-3, wants to fill moment-2
	postsByMomentID := map[string]bool{
		"moment-1": true,
		"moment-3": true,
	}

	gapsToFill := []*pbUser.StreakGap{
		{MomentId: "moment-2"},
	}

	// We'll simulate the calculateEstimatedStreak logic
	gapMomentIDs := make(map[string]bool)
	for _, gap := range gapsToFill {
		gapMomentIDs[gap.MomentId] = true
	}

	consecutiveDays := 0
	for _, moment := range sortedMoments {
		hasPost := postsByMomentID[moment.ID] || gapMomentIDs[moment.ID]
		if hasPost {
			consecutiveDays++
		} else {
			break
		}
	}

	estimatedStreak := uint64(consecutiveDays)

	// Should count moment-1 (posted), moment-2 (gap filled), moment-3 (posted), then break at moment-4
	assert.Equal(t, uint64(3), estimatedStreak)

	// Test scenario 2: No gaps to fill
	gapMomentIDs = make(map[string]bool)

	consecutiveDays = 0
	for _, moment := range sortedMoments {
		hasPost := postsByMomentID[moment.ID] || gapMomentIDs[moment.ID]
		if hasPost {
			consecutiveDays++
		} else {
			break
		}
	}

	estimatedStreak = uint64(consecutiveDays)

	// Should only count moment-1 (posted), then break at moment-2 (not posted, not filled)
	assert.Equal(t, uint64(1), estimatedStreak)
}

func TestCalculateEstimatedStreakScenarios(t *testing.T) {
	// Test various streak calculation scenarios
	now := time.Now()
	sortedMoments := []models.Moment{
		{ID: "moment-1", FiredAt: &now},
		{ID: "moment-2", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -1); return &t }()},
		{ID: "moment-3", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -2); return &t }()},
		{ID: "moment-4", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -3); return &t }()},
		{ID: "moment-5", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -4); return &t }()},
	}

	testCases := []struct {
		name           string
		posts          map[string]bool
		gaps           []string
		expectedStreak uint64
	}{
		{
			name:           "Perfect streak, no gaps needed",
			posts:          map[string]bool{"moment-1": true, "moment-2": true, "moment-3": true, "moment-4": true, "moment-5": true},
			gaps:           []string{},
			expectedStreak: 5,
		},
		{
			name:           "Single gap in middle",
			posts:          map[string]bool{"moment-1": true, "moment-3": true, "moment-4": true, "moment-5": true},
			gaps:           []string{"moment-2"},
			expectedStreak: 5,
		},
		{
			name:           "Multiple gaps",
			posts:          map[string]bool{"moment-1": true, "moment-4": true},
			gaps:           []string{"moment-2", "moment-3"},
			expectedStreak: 4,
		},
		{
			name:           "Gap at beginning",
			posts:          map[string]bool{"moment-2": true, "moment-3": true, "moment-4": true},
			gaps:           []string{"moment-1"},
			expectedStreak: 4,
		},
		{
			name:           "No posts, all gaps",
			posts:          map[string]bool{},
			gaps:           []string{"moment-1", "moment-2", "moment-3"},
			expectedStreak: 3,
		},
		{
			name:           "Break in streak",
			posts:          map[string]bool{"moment-1": true, "moment-2": true},
			gaps:           []string{},
			expectedStreak: 2, // Breaks at moment-3
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Convert gaps to the format expected by the function
			gapsToFill := make([]*pbUser.StreakGap, len(tc.gaps))
			for i, gapMomentID := range tc.gaps {
				gapsToFill[i] = &pbUser.StreakGap{MomentId: gapMomentID}
			}

			// Simulate calculateEstimatedStreak logic
			gapMomentIDs := make(map[string]bool)
			for _, gap := range gapsToFill {
				gapMomentIDs[gap.MomentId] = true
			}

			consecutiveDays := 0
			for _, moment := range sortedMoments {
				hasPost := tc.posts[moment.ID] || gapMomentIDs[moment.ID]
				if hasPost {
					consecutiveDays++
				} else {
					break
				}
			}

			estimatedStreak := uint64(consecutiveDays)
			assert.Equal(t, tc.expectedStreak, estimatedStreak, "Test case: %s", tc.name)
		})
	}
}

func TestGapIdentificationScenarios(t *testing.T) {
	// Test gap identification logic that would be used in CalculateStreakRecovery
	now := time.Now()
	lookbackDays := 7
	lookbackAgo := now.AddDate(0, 0, -lookbackDays)

	// Create moments within and outside lookback period
	moments := []models.Moment{
		{ID: "moment-today", FiredAt: &now},
		{ID: "moment-yesterday", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -1); return &t }()},
		{ID: "moment-2days", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -2); return &t }()},
		{ID: "moment-old", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -10); return &t }()}, // Outside lookback
	}

	// User posted for today and 2 days ago
	postsByMomentID := map[string]bool{
		"moment-today": true,
		"moment-2days": true,
	}

	// Simulate gap identification logic
	var gapsToFill []*pbUser.StreakGap
	dateAdded := make(map[string]bool)

	for _, moment := range moments {
		// Only consider moments from the last N days
		if moment.FiredAt.Before(lookbackAgo) {
			continue
		}

		dateKey := moment.FiredAt.Format("2006-01-02")
		if dateAdded[dateKey] {
			continue
		}

		// Check if user posted for this moment
		if !postsByMomentID[moment.ID] {
			gapsToFill = append(gapsToFill, &pbUser.StreakGap{
				Date: &pbCommon.Date{
					Year:  int32(moment.FiredAt.Year()),
					Month: int32(moment.FiredAt.Month()),
					Day:   int32(moment.FiredAt.Day()),
				},
				MomentId: moment.ID,
			})
			dateAdded[dateKey] = true
		}
	}

	// Should find gap for yesterday only (today and 2 days ago have posts, old moment is outside lookback)
	require.Len(t, gapsToFill, 1)
	assert.Equal(t, "moment-yesterday", gapsToFill[0].MomentId)

	// Test eligibility logic
	isEligible := len(gapsToFill) > 0 && len(gapsToFill) <= 10
	assert.True(t, isEligible)

	// Test with too many gaps
	manyGaps := make([]*pbUser.StreakGap, 11)
	for i := 0; i < 11; i++ {
		manyGaps[i] = &pbUser.StreakGap{MomentId: "moment-" + string(rune(i))}
	}
	isEligible = len(manyGaps) > 0 && len(manyGaps) <= 10
	assert.False(t, isEligible)
}

func TestUserPostStructure(t *testing.T) {
	// Test the UserPost struct and conversion logic
	userPosts := []UserPost{
		{
			PostID:    "post1",
			UserID:    "user123",
			MomentID:  gS.NullString{StringVal: "moment-1", Valid: true},
			CreatedAt: time.Now(),
		},
		{
			PostID:    "post2",
			UserID:    "user123",
			MomentID:  gS.NullString{Valid: false}, // No moment ID
			CreatedAt: time.Now(),
		},
	}

	// Create map like in the actual implementation
	postsByMomentID := make(map[string]bool)
	for _, post := range userPosts {
		if post.MomentID.Valid {
			postsByMomentID[post.MomentID.StringVal] = true
		}
	}

	// Should only have one entry (for moment-1)
	assert.Len(t, postsByMomentID, 1)
	assert.True(t, postsByMomentID["moment-1"])
	assert.False(t, postsByMomentID["moment-nonexistent"])
}

// Helper function to sort gaps by date (replicating the original function)
func sortGapsByDate(gaps []*pbUser.StreakGap) {
	sort.Slice(gaps, func(i, j int) bool {
		dateI := gaps[i].Date
		dateJ := gaps[j].Date

		if dateI.Year != dateJ.Year {
			return dateI.Year < dateJ.Year
		}
		if dateI.Month != dateJ.Month {
			return dateI.Month < dateJ.Month
		}
		return dateI.Day < dateJ.Day
	})
}

func TestDateSortingEdgeCases(t *testing.T) {
	// Test sorting with same year, different months
	gaps := []*pbUser.StreakGap{
		{Date: &pbCommon.Date{Year: 2023, Month: 12, Day: 1}, MomentId: "moment-dec"},
		{Date: &pbCommon.Date{Year: 2023, Month: 1, Day: 1}, MomentId: "moment-jan"},
		{Date: &pbCommon.Date{Year: 2023, Month: 6, Day: 1}, MomentId: "moment-jun"},
	}

	sortGapsByDate(gaps)

	assert.Equal(t, "moment-jan", gaps[0].MomentId)
	assert.Equal(t, "moment-jun", gaps[1].MomentId)
	assert.Equal(t, "moment-dec", gaps[2].MomentId)

	// Test sorting with same year and month, different days
	gaps = []*pbUser.StreakGap{
		{Date: &pbCommon.Date{Year: 2023, Month: 1, Day: 15}, MomentId: "moment-15th"},
		{Date: &pbCommon.Date{Year: 2023, Month: 1, Day: 1}, MomentId: "moment-1st"},
		{Date: &pbCommon.Date{Year: 2023, Month: 1, Day: 10}, MomentId: "moment-10th"},
	}

	sortGapsByDate(gaps)

	assert.Equal(t, "moment-1st", gaps[0].MomentId)
	assert.Equal(t, "moment-10th", gaps[1].MomentId)
	assert.Equal(t, "moment-15th", gaps[2].MomentId)

	// Test sorting across different years
	gaps = []*pbUser.StreakGap{
		{Date: &pbCommon.Date{Year: 2024, Month: 1, Day: 1}, MomentId: "moment-2024"},
		{Date: &pbCommon.Date{Year: 2022, Month: 12, Day: 31}, MomentId: "moment-2022"},
		{Date: &pbCommon.Date{Year: 2023, Month: 6, Day: 15}, MomentId: "moment-2023"},
	}

	sortGapsByDate(gaps)

	assert.Equal(t, "moment-2022", gaps[0].MomentId)
	assert.Equal(t, "moment-2023", gaps[1].MomentId)
	assert.Equal(t, "moment-2024", gaps[2].MomentId)
}

func TestValidationLogic(t *testing.T) {
	// Test the validation logic used in ApplyStreakRecovery
	testCases := []struct {
		name        string
		userID      string
		gaps        []*pbUser.StreakGap
		shouldError bool
		errorMsg    string
	}{
		{
			name:        "Valid request",
			userID:      "user123",
			gaps:        []*pbUser.StreakGap{{MomentId: "moment-1"}},
			shouldError: false,
		},
		{
			name:        "Empty user ID",
			userID:      "",
			gaps:        []*pbUser.StreakGap{{MomentId: "moment-1"}},
			shouldError: true,
			errorMsg:    "invalid user ID: empty",
		},
		{
			name:        "No gaps",
			userID:      "user123",
			gaps:        []*pbUser.StreakGap{},
			shouldError: true,
			errorMsg:    "no gaps provided for recovery",
		},
		{
			name:        "Too many gaps",
			userID:      "user123",
			gaps:        make([]*pbUser.StreakGap, 11), // More than 10
			shouldError: true,
			errorMsg:    "too many gaps for recovery",
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Simulate validation logic from ApplyStreakRecovery
			var err error

			if tc.userID == "" {
				err = errors.New("invalid user ID: empty")
			} else if len(tc.gaps) == 0 {
				err = errors.New("no gaps provided for recovery")
			} else if len(tc.gaps) > 10 {
				err = errors.New("too many gaps for recovery")
			}

			if tc.shouldError {
				require.Error(t, err)
				assert.Contains(t, err.Error(), tc.errorMsg)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestMomentFiltering(t *testing.T) {
	// Test the logic for filtering fired vs unfired moments
	now := time.Now()
	moments := []models.Moment{
		{ID: "fired-1", FiredAt: &now},
		{ID: "fired-2", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -1); return &t }()},
		{ID: "unfired-1", FiredAt: nil},
		{ID: "fired-3", FiredAt: func() *time.Time { t := now.AddDate(0, 0, -2); return &t }()},
		{ID: "unfired-2", FiredAt: nil},
	}

	// Simulate the filtering logic from CalculateStreakRecovery
	var sortedMoments []models.Moment
	var unfiredCount int

	for _, moment := range moments {
		if moment.FiredAt != nil {
			sortedMoments = append(sortedMoments, moment)
		} else {
			unfiredCount++
		}
	}

	// Should have 3 fired moments and 2 unfired
	assert.Len(t, sortedMoments, 3)
	assert.Equal(t, 2, unfiredCount)

	// Verify only fired moments are included
	for _, moment := range sortedMoments {
		assert.NotNil(t, moment.FiredAt)
		assert.Contains(t, []string{"fired-1", "fired-2", "fired-3"}, moment.ID)
	}
}
