package internal

import (
	"context"
	"fmt"
	"sync"
	"testing"

	gS "cloud.google.com/go/spanner"
	"github.com/BeReal-App/backend-go/domains/entity"
	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Mock SpannerClient for testing
type MockSpannerClient struct {
	mock.Mock
}

func (m *MockSpannerClient) Single() *MockReadonlyTransaction {
	args := m.Called()
	return args.Get(0).(*MockReadonlyTransaction)
}

func (m *MockSpannerClient) ReadWriteTransactionWithOptions(ctx context.Context, f func(context.Context, *gS.ReadWriteTransaction) error, opts gS.TransactionOptions) (gS.CommitResponse, error) {
	args := m.Called(ctx, mock.Anything, opts)
	// Execute the function with a nil transaction - we're just testing it gets called
	_ = f(ctx, nil)
	return args.Get(0).(gS.CommitResponse), args.Error(1)
}

// Mock ReadonlyTransaction for testing
type MockReadonlyTransaction struct {
	mock.Mock
}

func (m *MockReadonlyTransaction) ReadRow(ctx context.Context, table string, key gS.Key, columns []string) (*gS.Row, error) {
	args := m.Called(ctx, table, key, columns)
	return args.Get(0).(*gS.Row), args.Error(1)
}

func (m *MockReadonlyTransaction) Query(ctx context.Context, statement gS.Statement) *MockRowIterator {
	args := m.Called(ctx, statement)
	return args.Get(0).(*MockRowIterator)
}

// Mock RowIterator for testing
type MockRowIterator struct {
	mock.Mock
}

func (m *MockRowIterator) Do(f func(*gS.Row) error) error {
	args := m.Called(mock.Anything)
	return args.Error(0)
}

func (m *MockRowIterator) Stop() {}

// MockMomentsClient is a mock for the moments client
type MockMomentsClient struct {
	Moments []models.Moment
}

func (m *MockMomentsClient) GetCurrentAndPreviousByRegion() []models.Moment {
	return m.Moments
}

// MockRow creates a mock Spanner row for testing
func MockRow(values map[string]any) *gS.Row {
	return &gS.Row{}
}

// TestServer embeds the Server struct to allow adding mock methods
type TestServer struct {
	Server
}

func TestIsStreakOutdated(t *testing.T) {
	// Create mock current and previous moments slice
	currentAndPreviousMoments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "previous-moment-2"},
		{ID: "current-moment-123"},
	}

	tests := []struct {
		name             string
		lastPostMomentID string
		moments          []models.Moment
		expected         bool
	}{
		{
			name:             "moment exists in current/previous moments - should not be outdated",
			lastPostMomentID: "current-moment-1",
			moments:          currentAndPreviousMoments,
			expected:         false, // not outdated because it exists in current/previous moments
		},
		{
			name:             "moment does not exist in current/previous moments - should be outdated",
			lastPostMomentID: "old-moment-456",
			moments:          currentAndPreviousMoments,
			expected:         true, // outdated because it doesn't exist in current/previous moments
		},
		{
			name:             "another current moment - should not be outdated",
			lastPostMomentID: "current-moment-123",
			moments:          currentAndPreviousMoments,
			expected:         false, // not outdated because it exists in current/previous moments
		},
		{
			name:             "nil moments - should not be outdated",
			lastPostMomentID: "any-moment-id",
			moments:          nil,
			expected:         false, // not outdated because we don't have moments data to verify against
		},
		{
			name:             "empty moments - should be outdated",
			lastPostMomentID: "any-moment-id",
			moments:          []models.Moment{},
			expected:         true, // outdated because moments is empty
		},
		{
			name:             "whitespace only lastPostMomentID - should be outdated",
			lastPostMomentID: "   ",
			moments:          currentAndPreviousMoments,
			expected:         true, // outdated because trimmed ID doesn't exist
		},
		{
			name:             "case sensitive moment ID check",
			lastPostMomentID: "Current-Moment-1", // different case
			moments:          currentAndPreviousMoments,
			expected:         true, // outdated because case doesn't match
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := isStreakOutdated(context.Background(), tt.lastPostMomentID, tt.moments)
			require.Equal(t, tt.expected, result)
		})
	}
}

func TestCorrectStreak(t *testing.T) {
	// Create mock current and previous moments slice
	currentAndPreviousMoments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "previous-moment-2"},
	}

	tests := []struct {
		name        string
		streak      entity.UserStreak
		moments     []models.Moment
		shouldReset bool
	}{
		{
			name: "streak with moment in current/previous moments - should not reset",
			streak: entity.UserStreak{
				Length:              5,
				LastPostMomentID:    gS.NullString{StringVal: "current-moment-1", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: false,
		},
		{
			name: "streak with moment not in current/previous moments - should reset",
			streak: entity.UserStreak{
				Length:              5,
				LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: true,
		},
		{
			name: "streak with invalid moment ID - should not reset",
			streak: entity.UserStreak{
				Length:              5,
				LastPostMomentID:    gS.NullString{Valid: false},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: false,
		},
		{
			name: "streak with zero length - should not reset",
			streak: entity.UserStreak{
				Length:              0,
				LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: false,
		},
		{
			name: "streak with nil moments - should not reset",
			streak: entity.UserStreak{
				Length:              5,
				LastPostMomentID:    gS.NullString{StringVal: "any-moment-id", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     nil,
			shouldReset: false,
		},
		{
			name: "streak with empty moments - should reset",
			streak: entity.UserStreak{
				Length:              10,
				LastPostMomentID:    gS.NullString{StringVal: "any-moment-id", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     []models.Moment{},
			shouldReset: true,
		},
		{
			name: "negative streak length - should not reset",
			streak: entity.UserStreak{
				Length:              -1,
				LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
				LastPostCalendarDay: gS.NullDate{Valid: true},
			},
			moments:     currentAndPreviousMoments,
			shouldReset: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			correctedStreak := correctStreak(context.Background(), tt.streak, tt.moments)
			if tt.shouldReset {
				require.Equal(t, uint64(0), correctedStreak.Length)
				require.Nil(t, correctedStreak.LastPostCalendarDay)
			} else {
				require.Equal(t, uint64(tt.streak.Length), correctedStreak.Length)
			}
		})
	}
}

// Test to verify that modifying the input streak doesn't affect the original
func TestCorrectStreakDoesNotModifyOriginal(t *testing.T) {
	originalStreak := entity.UserStreak{
		UserID:              "test-user",
		Length:              5,
		LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
		LastPostCalendarDay: gS.NullDate{Valid: true},
	}

	// Create a copy to compare later
	streakBeforeCall := originalStreak

	// Call correctStreak with moments that should cause a reset
	moments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "current-moment-2"},
	}

	correctedStreak := correctStreak(context.Background(), originalStreak, moments)

	// Verify the corrected streak is reset
	require.Equal(t, uint64(0), correctedStreak.Length)

	// Verify the original streak is unchanged
	require.Equal(t, streakBeforeCall.Length, originalStreak.Length)
	require.Equal(t, streakBeforeCall.LastPostMomentID, originalStreak.LastPostMomentID)
	require.Equal(t, streakBeforeCall.LastPostCalendarDay, originalStreak.LastPostCalendarDay)
}

// Test edge cases for moment ID matching
func TestMomentIDMatching(t *testing.T) {
	tests := []struct {
		name       string
		momentID   string
		moments    []models.Moment
		shouldFind bool
	}{
		{
			name:       "exact match",
			momentID:   "moment-123",
			moments:    []models.Moment{{ID: "moment-123"}},
			shouldFind: true,
		},
		{
			name:       "no match - different ID",
			momentID:   "moment-456",
			moments:    []models.Moment{{ID: "moment-123"}},
			shouldFind: false,
		},
		{
			name:       "case sensitive - different case",
			momentID:   "Moment-123",
			moments:    []models.Moment{{ID: "moment-123"}},
			shouldFind: false,
		},
		{
			name:       "moment with empty ID in slice",
			momentID:   "moment-123",
			moments:    []models.Moment{{ID: ""}},
			shouldFind: false,
		},
		{
			name:       "multiple moments - first match",
			momentID:   "moment-123",
			moments:    []models.Moment{{ID: "moment-123"}, {ID: "moment-456"}},
			shouldFind: true,
		},
		{
			name:       "multiple moments - last match",
			momentID:   "moment-456",
			moments:    []models.Moment{{ID: "moment-123"}, {ID: "moment-456"}},
			shouldFind: true,
		},
		{
			name:       "unicode characters",
			momentID:   "moment-123-🚀",
			moments:    []models.Moment{{ID: "moment-123-🚀"}},
			shouldFind: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			isOutdated := isStreakOutdated(context.Background(), tt.momentID, tt.moments)
			expected := !tt.shouldFind // isStreakOutdated returns true when moment is NOT found
			require.Equal(t, expected, isOutdated)
		})
	}
}

// Test context behavior
func TestContextHandling(t *testing.T) {
	// Test with cancelled context
	ctx, cancel := context.WithCancel(context.Background())
	cancel() // Cancel immediately

	moments := []models.Moment{{ID: "test-moment"}}

	// Function should still work even with cancelled context
	result := isStreakOutdated(ctx, "non-existent-moment", moments)
	require.True(t, result)
}

// Benchmark tests
func BenchmarkIsStreakOutdated(b *testing.B) {
	moments := make([]models.Moment, 100)
	for i := range 100 {
		moments[i] = models.Moment{ID: fmt.Sprintf("moment-%d", i)}
	}

	ctx := context.Background()

	b.ResetTimer()
	for b.Loop() {
		isStreakOutdated(ctx, "moment-50", moments)
	}
}

func BenchmarkCorrectStreak(b *testing.B) {
	moments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "previous-moment-2"},
	}

	streak := entity.UserStreak{
		Length:              5,
		LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
		LastPostCalendarDay: gS.NullDate{Valid: true},
	}

	ctx := context.Background()

	b.ResetTimer()
	for b.Loop() {
		correctStreak(ctx, streak, moments)
	}
}

// Test concurrent access to verify race condition behavior
func TestCorrectStreakConcurrentAccess(t *testing.T) {
	const numGoroutines = 100
	const numIterations = 10

	originalStreak := entity.UserStreak{
		UserID:              "test-user",
		Length:              5,
		LastPostMomentID:    gS.NullString{StringVal: "old-moment-123", Valid: true},
		LastPostCalendarDay: gS.NullDate{Valid: true},
	}

	moments := []models.Moment{
		{ID: "current-moment-1"},
		{ID: "current-moment-2"},
	}

	// Run many goroutines concurrently to try to expose race conditions
	var wg sync.WaitGroup
	ctx := context.Background()

	for range numGoroutines {
		wg.Add(1)
		go func() {
			defer wg.Done()
			for range numIterations {
				correctedStreak := correctStreak(ctx, originalStreak, moments)
				// All should be reset since moment doesn't exist in moments slice
				require.Equal(t, uint64(0), correctedStreak.Length)
			}
		}()
	}

	wg.Wait()

	// Verify original streak is still unchanged after all concurrent calls
	require.Equal(t, int64(5), originalStreak.Length)
	require.Equal(t, "old-moment-123", originalStreak.LastPostMomentID.StringVal)
}

// Test large numbers of moments for performance
func TestIsStreakOutdatedLargeDataset(t *testing.T) {
	// Create a large slice of moments
	moments := make([]models.Moment, 10000)
	for i := range 10000 {
		moments[i] = models.Moment{ID: fmt.Sprintf("moment-%d", i)}
	}

	ctx := context.Background()

	// Test finding a moment at the beginning
	result := isStreakOutdated(ctx, "moment-0", moments)
	require.False(t, result)

	// Test finding a moment in the middle
	result = isStreakOutdated(ctx, "moment-5000", moments)
	require.False(t, result)

	// Test finding a moment at the end
	result = isStreakOutdated(ctx, "moment-9999", moments)
	require.False(t, result)

	// Test not finding a moment
	result = isStreakOutdated(ctx, "moment-10000", moments)
	require.True(t, result)
}
