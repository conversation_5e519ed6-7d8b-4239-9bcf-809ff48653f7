package internal

import (
	"context"
	"fmt"

	gS "cloud.google.com/go/spanner"
	entity "github.com/BeReal-App/backend-go/domains/entity"
	"github.com/BeReal-App/backend-go/domains/entity/moment-grpc-api/models"
	"github.com/BeReal-App/backend-go/domains/entity/user-grpc-api/internal/metrics"
	pbUser "github.com/BeReal-App/backend-go/proto/entity/user/v1"
	"github.com/BeReal-App/backend-go/shared/scope"
	"github.com/BeReal-App/backend-go/shared/util"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func (s *Server) GetStreak(ctx context.Context, req *pbUser.GetStreakRequest) (*pbUser.GetStreakResponse, error) {
	row, err := s.spannerClient.Single().ReadRowWithOptions(
		ctx,
		entity.UserStreakTableName,
		gS.Key{req.UserId},
		entity.UserStreakDefaultMapper.Columns(),
		&gS.ReadOptions{RequestTag: "GetStreak"},
	)
	if err != nil {
		if gS.ErrCode(err) == codes.NotFound {
			return nil, status.Errorf(codes.NotFound, "UserStreak not found [userId=%s]", req.UserId)
		}
		return nil, fmt.Errorf("Single().ReadRowWithOptions() [userId=%s]: %w", req.UserId, err)
	}

	var streak entity.UserStreak
	if err = row.Columns(entity.UserStreakDefaultMapper.Addrs(&streak)...); err != nil {
		return nil, fmt.Errorf("row.Columns() [userId=%s]: %w", req.UserId, err)
	}

	// Get current and previous moments with error handling
	moments := s.momentsClient.GetCurrentAndPreviousByRegion()
	if moments == nil {
		// Log warning but don't fail the request - fallback to not resetting the streak
		l := scope.GetLoggerForCallsite(ctx, "GetStreak")
		l.Warn().Msg("failed to get current and previous moments, using empty slice")
		moments = []models.Moment{}
	}

	return &pbUser.GetStreakResponse{
		Streak: correctStreak(ctx, streak, moments),
	}, nil
}

func (s *Server) GetStreaks(ctx context.Context, req *pbUser.GetStreaksRequest) (*pbUser.GetStreaksResponse, error) {
	userIDs := req.GetUserIds()
	streaks := make(map[string]*pbUser.Streak)

	// Get current and previous moments with error handling
	moments := s.momentsClient.GetCurrentAndPreviousByRegion()
	if moments == nil {
		// Log warning but don't fail the request - fallback to not resetting the streak
		l := scope.GetLoggerForCallsite(ctx, "GetStreaks")
		l.Warn().Msg("failed to get current and previous moments, using empty slice")
		moments = []models.Moment{}
	}

	// Convert userIDs to Spanner KeySet
	keys := make([]gS.Key, len(userIDs))
	for i, id := range userIDs {
		keys[i] = gS.Key{id}
	}
	keySet := gS.KeySetFromKeys(keys...)

	// Use ReadWithOptions for efficient key-based reads
	iter := s.spannerClient.Single().ReadWithOptions(
		ctx,
		entity.UserStreakTableName,
		keySet,
		entity.UserStreakDefaultMapper.Columns(),
		&gS.ReadOptions{RequestTag: "GetStreaks"},
	)

	err := iter.Do(func(row *gS.Row) error {
		var streak entity.UserStreak
		if err := row.Columns(entity.UserStreakDefaultMapper.Addrs(&streak)...); err != nil {
			return fmt.Errorf("row.Columns() [userId=%s]: %w", streak.UserID, err)
		}
		streaks[streak.UserID] = correctStreak(ctx, streak, moments)
		return nil
	})
	if err != nil {
		return nil, err
	}

	return &pbUser.GetStreaksResponse{
		Streaks: streaks,
	}, nil
}

func (s *Server) UpdateStreak(ctx context.Context, req *pbUser.UpdateStreakRequest) (*pbUser.UpdateStreakResponse, error) {
	e := entity.UserStreak{
		UserID:              req.UserId,
		Length:              int64(req.Length),
		LastPostCalendarDay: req.LastPostCalendarDay.SpannerNullDate(),
		LastPostMomentID:    gS.NullString{StringVal: req.LastPostMomentId, Valid: req.LastPostMomentId != ""},
		UpdatedAt:           gS.CommitTimestamp,
		UpdatedBy:           util.UpdationSourceFromCtx(ctx),
	}

	resp, err := s.spannerClient.ReadWriteTransactionWithOptions(
		ctx,
		func(_ context.Context, tx *gS.ReadWriteTransaction) error {
			return tx.BufferWrite([]*gS.Mutation{gS.InsertOrUpdate(
				entity.UserStreakTableName,
				entity.UserStreakDefaultMapper.Columns(),
				entity.UserStreakDefaultMapper.Values(&e),
			)})
		},
		gS.TransactionOptions{
			CommitOptions:  gS.CommitOptions{MaxCommitDelay: s.streakCommitDelay, ReturnCommitStats: false},
			TransactionTag: "UpdateStreak",
		},
	)
	if err != nil {
		return nil, fmt.Errorf("on s.spannerClient.ReadWriteTransactionWithOptions() [userId=%s]: %w", req.UserId, err)
	}

	return &pbUser.UpdateStreakResponse{
		UpdatedAt: timestamppb.New(resp.CommitTs),
	}, nil
}

func correctStreak(ctx context.Context, streak entity.UserStreak, moments []models.Moment) *pbUser.Streak {
	// Create a copy to avoid modifying the original parameter
	streakCopy := streak

	if streakCopy.Length > 0 && streakCopy.LastPostMomentID.Valid {
		isOutdated := isStreakOutdated(ctx, streakCopy.LastPostMomentID.StringVal, moments)

		// Track metric for outdated/not outdated streaks
		if isOutdated {
			metrics.StreakStatusCounter.WithLabelValues("true").Inc()
			streakCopy.Length = 0
			streakCopy.LastPostCalendarDay = gS.NullDate{}
		} else {
			metrics.StreakStatusCounter.WithLabelValues("false").Inc()
		}
	}

	return streakCopy.Proto()
}

// isStreakOutdated checks if the streak is outdated by verifying if the LastPostMomentID does NOT exist
// in the current and previous moments. If the last post was made in an older moment (not in current or previous),
// then the streak should be reset to 0.
func isStreakOutdated(ctx context.Context, lastPostMomentID string, moments []models.Moment) bool {
	l := scope.GetLoggerForCallsite(ctx, "isStreakOutdated")

	if moments == nil {
		l.Warn().Msg("moments slice is nil, considering streak not outdated")
		return false
	}

	// Check if the LastPostMomentID exists in current and previous moments
	for _, moment := range moments {
		if moment.ID == lastPostMomentID {
			return false
		}
	}

	return true
}
